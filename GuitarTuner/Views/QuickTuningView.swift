import SwiftUI

struct QuickTuningView: View {
    @ObservedObject var viewModel: MainViewModel
    @State private var showTuningSelector = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 频率显示区域
            FrequencyDisplayView(
                frequency: viewModel.audioManager.currentFrequency,
                amplitude: viewModel.audioManager.currentAmplitude
            )
            
            // 调音结果显示
            TuningResultView(
                result: viewModel.tuningAnalyzer.currentResult,
                accuracy: viewModel.tuningAnalyzer.tuningAccuracy,
                cents: viewModel.tuningAnalyzer.cents
            )
            
            // 吉他头界面
            GuitarHeadView(
                currentString: viewModel.tuningAnalyzer.detectedString,
                accuracy: viewModel.tuningAnalyzer.tuningAccuracy,
                tuning: viewModel.currentTuning
            )
            
            // 控制按钮
            ControlButtonsView(
                isRecording: viewModel.isRecording,
                onToggleTuning: viewModel.toggleTuning,
                onShowTuningSelector: { showTuningSelector = true }
            )
        }
        .padding()
        .sheet(isPresented: $showTuningSelector) {
            TuningSelectorView(
                selectedTuning: Binding(
                    get: { viewModel.currentTuning },
                    set: { viewModel.setTuning($0) }
                )
            )
        }
    }
}

// MARK: - Frequency Display View
struct FrequencyDisplayView: View {
    let frequency: Float
    let amplitude: Float
    
    var body: some View {
        VStack(spacing: 10) {
            Text("当前频率")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text(String(format: "%.1f Hz", frequency))
                .font(.system(size: 36, weight: .bold, design: .monospaced))
                .foregroundColor(amplitude > 0.01 ? .primary : .secondary)
            
            // 振幅指示器
            AmplitudeIndicator(amplitude: amplitude)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Amplitude Indicator
struct AmplitudeIndicator: View {
    let amplitude: Float
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<10, id: \.self) { index in
                Rectangle()
                    .fill(barColor(for: index))
                    .frame(width: 4, height: CGFloat(barHeight(for: index)))
                    .cornerRadius(2)
            }
        }
        .frame(height: 30)
    }
    
    private func barColor(for index: Int) -> Color {
        let threshold = Float(index) / 10.0
        return amplitude > threshold ? .green : .gray.opacity(0.3)
    }
    
    private func barHeight(for index: Int) -> Int {
        let threshold = Float(index) / 10.0
        return amplitude > threshold ? 20 + index * 2 : 4
    }
}

// MARK: - Tuning Result View
struct TuningResultView: View {
    let result: TuningResult?
    let accuracy: TuningAccuracy
    let cents: Float
    
    var body: some View {
        VStack(spacing: 8) {
            if let result = result {
                Text(result.detectedString?.name ?? "")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(accuracy.description)
                    .font(.headline)
                    .foregroundColor(accuracyColor)
                
                Text(String(format: "%.1f 音分", cents))
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                Text("等待音频输入...")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var accuracyColor: Color {
        switch accuracy {
        case .perfect: return .green
        case .tooLow: return .blue
        case .tooHigh: return .red
        case .outOfRange: return .gray
        }
    }
}

// MARK: - Guitar Head View
struct GuitarHeadView: View {
    let currentString: GuitarString?
    let accuracy: TuningAccuracy
    let tuning: GuitarTuning
    
    var body: some View {
        VStack(spacing: 15) {
            Text("吉他调音")
                .font(.title2)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
                ForEach(GuitarString.allCases, id: \.self) { string in
                    GuitarStringView(
                        string: string,
                        isActive: currentString == string,
                        accuracy: accuracy,
                        frequency: tuning.frequencies[string] ?? 0.0
                    )
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - Guitar String View
struct GuitarStringView: View {
    let string: GuitarString
    let isActive: Bool
    let accuracy: TuningAccuracy
    let frequency: Float
    
    var body: some View {
        VStack(spacing: 8) {
            // 调音旋钮
            Circle()
                .fill(isActive ? accuracyColor : Color.gray.opacity(0.3))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(string.note)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                )
                .scaleEffect(isActive ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: isActive)
            
            // 弦号
            Text("\(string.rawValue)")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            // 频率
            Text(String(format: "%.0f", frequency))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    private var accuracyColor: Color {
        switch accuracy {
        case .perfect: return .green
        case .tooLow: return .blue
        case .tooHigh: return .red
        case .outOfRange: return .orange
        }
    }
}

// MARK: - Control Buttons View
struct ControlButtonsView: View {
    let isRecording: Bool
    let onToggleTuning: () -> Void
    let onShowTuningSelector: () -> Void
    
    var body: some View {
        HStack(spacing: 20) {
            // 调音选择按钮
            Button(action: onShowTuningSelector) {
                HStack {
                    Image(systemName: "music.note.list")
                    Text("调音模式")
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            
            // 开始/停止调音按钮
            Button(action: onToggleTuning) {
                HStack {
                    Image(systemName: isRecording ? "stop.circle.fill" : "play.circle.fill")
                    Text(isRecording ? "停止" : "开始")
                }
                .padding()
                .background(isRecording ? Color.red : Color.green)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
    }
}

// MARK: - Tuning Selector View
struct TuningSelectorView: View {
    @Binding var selectedTuning: GuitarTuning
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List(GuitarTuning.allCases, id: \.self) { tuning in
                Button(action: {
                    selectedTuning = tuning
                    dismiss()
                }) {
                    HStack {
                        Text(tuning.rawValue)
                        Spacer()
                        if selectedTuning == tuning {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .navigationTitle("选择调音模式")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            #endif
        }
    }
}

#Preview {
    QuickTuningView(viewModel: MainViewModel())
} 