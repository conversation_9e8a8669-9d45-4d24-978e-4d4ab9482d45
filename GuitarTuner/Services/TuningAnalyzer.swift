import Foundation
import Combine

class TuningAnalyzer: ObservableObject {
    // MARK: - Published Properties
    @Published var currentResult: TuningResult?
    @Published var detectedString: GuitarString?
    @Published var tuningAccuracy: TuningAccuracy = .outOfRange
    @Published var cents: Float = 0.0
    
    // MARK: - Private Properties
    private var audioManager: AudioManager
    private var currentTuning: GuitarTuning = .standard
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(audioManager: AudioManager) {
        self.audioManager = audioManager
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        audioManager.$currentFrequency
            .combineLatest(audioManager.$currentAmplitude)
            .sink { [weak self] frequency, amplitude in
                self?.analyzeFrequency(frequency: frequency, amplitude: amplitude)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    func setTuning(_ tuning: GuitarTuning) {
        currentTuning = tuning
        analyzeCurrentFrequency()
    }
    
    func analyzeFrequency(frequency: Float, amplitude: Float) {
        // 如果振幅太小，重置结果
        if amplitude < 0.01 {
            resetResult()
            return
        }
        
        // 检测吉他弦
        let detectedString = audioManager.detectGuitarString(frequency: frequency, tuning: currentTuning)
        
        if let string = detectedString {
            let targetFrequency = currentTuning.frequencies[string] ?? 0.0
            let accuracy = audioManager.calculateTuningAccuracy(targetFreq: targetFrequency, actualFreq: frequency)
            let cents = calculateCents(targetFreq: targetFrequency, actualFreq: frequency)
            
            let result = TuningResult(
                detectedString: string,
                targetFrequency: targetFrequency,
                actualFrequency: frequency,
                accuracy: accuracy,
                cents: cents
            )
            
            DispatchQueue.main.async {
                self.currentResult = result
                self.detectedString = string
                self.tuningAccuracy = accuracy
                self.cents = cents
            }
        } else {
            resetResult()
        }
    }
    
    private func analyzeCurrentFrequency() {
        analyzeFrequency(frequency: audioManager.currentFrequency, amplitude: audioManager.currentAmplitude)
    }
    
    private func resetResult() {
        DispatchQueue.main.async {
            self.currentResult = nil
            self.detectedString = nil
            self.tuningAccuracy = .outOfRange
            self.cents = 0.0
        }
    }
    
    // MARK: - Utility Methods
    private func calculateCents(targetFreq: Float, actualFreq: Float) -> Float {
        guard actualFreq > 0 && targetFreq > 0 else { return 0.0 }
        return 1200 * log2(actualFreq / targetFreq)
    }
    
    func getTuningResult() -> TuningResult? {
        return currentResult
    }
    
    func isInTune() -> Bool {
        return tuningAccuracy == .perfect
    }
    
    func getTuningDirection() -> String {
        switch tuningAccuracy {
        case .perfect:
            return "完美"
        case .tooLow:
            return "需要调高"
        case .tooHigh:
            return "需要调低"
        case .outOfRange:
            return "超出范围"
        }
    }
    
    func getCentsDescription() -> String {
        let absCents = abs(cents)
        if absCents < 1 {
            return "完美"
        } else if absCents < 10 {
            return "接近"
        } else if absCents < 50 {
            return "需要微调"
        } else {
            return "需要大幅调整"
        }
    }
} 